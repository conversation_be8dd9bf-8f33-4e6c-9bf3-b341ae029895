[tool:pytest]
# Pytest configuration for Energy Generation Dashboard

asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --disable-warnings
    --tb=short
    --maxfail=10
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    api: Tests that require API access
    cache: Tests related to caching functionality
    ui: Tests for UI components
    data: Tests for data processing
    visualization: Tests for plotting and visualization
    config: Tests for configuration modules
    services: Tests for backend services

# Test timeout (in seconds)
timeout = 300

# Coverage settings
[coverage:run]
source = backend, frontend, src
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    setup.py
    conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2

[coverage:html]
directory = test_reports/coverage_html
